"""
MQTT Communication Module

This module provides MQTT communication capabilities for the waffles-backend system.
It handles device commands for electronic, system, sale, and storage operations.

Main components:
- client: MQTT client connection and message handling
- handlers: Command handlers for different device operations
- service: Service integration with FastAPI application lifecycle
"""

from .client import MQTTClient
from .service import mqtt_service

__all__ = ['MQTTClient', 'mqtt_service']
