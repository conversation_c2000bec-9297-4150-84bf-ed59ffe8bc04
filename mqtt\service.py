"""
MQTT Service Integration

Provides MQTT service integration with FastAPI application lifecycle.
Manages MQTT client startup and shutdown.
"""

import logging
from typing import Optional
from .client import MQTTClient
from .handlers import MQTT_HANDLERS

logger = logging.getLogger(__name__)


class MQTTService:
    """MQTT service for managing MQTT client lifecycle"""
    
    def __init__(self):
        self.client: Optional[MQTTClient] = None
        self.running = False
    
    def start(self):
        """Start the MQTT service"""
        if self.running:
            logger.warning("⚠️ MQTT service is already running")
            return
        
        try:
            logger.info("🚀 Starting MQTT service...")
            
            # Create MQTT client
            self.client = MQTTClient()
            
            # Set message handlers
            self.client.set_message_handlers(MQTT_HANDLERS)
            
            # Start the client
            self.client.start()
            
            self.running = True
            logger.info("✅ MQTT service started successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to start MQTT service: {e}")
            self.running = False
            raise
    
    def stop(self):
        """Stop the MQTT service"""
        if not self.running:
            logger.info("ℹ️ MQTT service is not running")
            return
        
        try:
            logger.info("🛑 Stopping MQTT service...")
            
            if self.client:
                self.client.stop()
                self.client = None
            
            self.running = False
            logger.info("✅ MQTT service stopped successfully")
            
        except Exception as e:
            logger.error(f"❌ Error stopping MQTT service: {e}")
    
    def is_running(self) -> bool:
        """Check if the MQTT service is running"""
        return self.running
    
    def is_connected(self) -> bool:
        """Check if the MQTT client is connected to the broker"""
        if not self.client:
            return False
        return self.client.is_connected()
    
    def get_status(self) -> dict:
        """Get MQTT service status"""
        return {
            "running": self.running,
            "connected": self.is_connected(),
            "client_id": self.client.client_id if self.client else None,
            "device_id": self.client.device_id if self.client else None,
            "broker": self.client.broker if self.client else None,
            "port": self.client.port if self.client else None
        }


# Global MQTT service instance
mqtt_service = MQTTService()
