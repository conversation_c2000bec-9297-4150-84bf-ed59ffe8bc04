"""
Test script for MQTT communication

This script demonstrates how to test the MQTT implementation.
You can use this to send test messages to the MQTT topics.
"""

import json
import time
import paho.mqtt.client as mqtt
from os import getenv
from dotenv import load_dotenv

load_dotenv()

# MQTT Configuration
BROKER = getenv("MQTT_HOST")
PORT = int(getenv("MQTT_PORT", 1883))
USERNAME = getenv("MQTT_USERNAME")
PASSWORD = getenv("MQTT_PASSWORD")
CLIENT_ID = "test-client"
DEVICE_ID = getenv("DEVICE_ID", "1234")

# Response topic to listen for responses
RESPONSE_TOPIC = f"devices/{DEVICE_ID}/responses"

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ Connected to MQTT broker")
        client.subscribe(RESPONSE_TOPIC)
        print(f"📡 Listening for responses on {RESPONSE_TOPIC}")
    else:
        print(f"❌ Failed to connect, return code: {rc}")

def on_message(client, userdata, msg):
    try:
        payload = json.loads(msg.payload.decode("utf-8"))
        print(f"📩 Response received: {payload}")
    except json.JSONDecodeError:
        print(f"❌ Failed to decode response: {msg.payload}")

def send_test_commands(client):
    """Send test commands to different topics"""
    
    # Test electronic commands
    print("\n🔧 Testing Electronic Commands:")
    
    # Section open
    topic = f"devices/{DEVICE_ID}/electronic/section_open"
    payload = {"section_id": 1}
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)
    
    # Check doors
    topic = f"devices/{DEVICE_ID}/electronic/check_doors"
    payload = {"section_id": 1}
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)
    
    # Unlock service
    topic = f"devices/{DEVICE_ID}/electronic/unlock_service"
    payload = {}
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)
    
    # Check service
    topic = f"devices/{DEVICE_ID}/electronic/check_service"
    payload = {}
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)
    
    # Test system commands
    print("\n💻 Testing System Commands:")
    
    # Reboot device
    topic = f"devices/{DEVICE_ID}/system/reboot_device"
    payload = {}
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)
    
    # Test sale commands
    print("\n🛒 Testing Sale Commands:")
    
    # Edit reservation
    topic = f"devices/{DEVICE_ID}/sale/edit_reservation"
    payload = {
        "uuid": "test-uuid-123",
        "status": 0,
        "max_days": 7
    }
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)
    
    # Test storage commands
    print("\n📦 Testing Storage Commands:")
    
    # Storage edit reservation
    topic = f"devices/{DEVICE_ID}/storage/storage_edit_reservation"
    payload = {
        "uuid": "test-storage-uuid-456",
        "status": 1,
        "max_days": 30
    }
    client.publish(topic, json.dumps(payload))
    print(f"📤 Sent to {topic}: {payload}")
    time.sleep(1)

def main():
    if not BROKER:
        print("❌ MQTT_HOST not configured in .env file")
        return
    
    print(f"🔗 Connecting to MQTT broker at {BROKER}:{PORT}")
    
    # Create MQTT client
    client = mqtt.Client(client_id=CLIENT_ID)
    
    # Set authentication if provided
    if USERNAME and PASSWORD:
        client.username_pw_set(USERNAME, PASSWORD)
    
    # Set callbacks
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # Connect to broker
        client.connect(BROKER, PORT, 60)
        
        # Start the loop in a separate thread
        client.loop_start()
        
        # Wait a moment for connection
        time.sleep(2)
        
        if client.is_connected():
            # Send test commands
            send_test_commands(client)
            
            # Wait for responses
            print("\n⏳ Waiting for responses (10 seconds)...")
            time.sleep(10)
        else:
            print("❌ Failed to connect to MQTT broker")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        client.loop_stop()
        client.disconnect()
        print("\n✅ Test completed")

if __name__ == "__main__":
    main()
