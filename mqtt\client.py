"""
MQTT Client Module

Handles MQTT connection, topic subscription, and message publishing.
Based on the structure from smazat.py but extended for multiple command types.
"""

import json
import logging
import threading
from typing import Optional, Callable, Dict, Any
from os import getenv
from dotenv import load_dotenv
import paho.mqtt.client as mqtt

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class MQTTClient:
    """MQTT Client for device communication"""
    
    def __init__(self):
        # MQTT Configuration from environment
        self.broker = getenv("MQTT_HOST")
        self.port = int(getenv("MQTT_PORT", 1883))
        self.username = getenv("MQTT_USERNAME")
        self.password = getenv("MQTT_PASSWORD")
        self.client_id = getenv("MQTT_CLIENT_ID", "waffles-backend")
        
        # Device configuration
        self.device_id = getenv("DEVICE_ID", "1234")
        
        # Topic patterns
        self.response_topic = f"devices/{self.device_id}/responses"
        self.command_topics = {
            "electronic": f"devices/{self.device_id}/electronic/+",
            "system": f"devices/{self.device_id}/system/+",
            "sale": f"devices/{self.device_id}/sale/+",
            "storage": f"devices/{self.device_id}/storage/+"
        }
        
        # MQTT client setup
        self.client: Optional[mqtt.Client] = None
        self.connected = False
        self.message_handlers: Dict[str, Callable] = {}
        
        # Threading
        self._thread: Optional[threading.Thread] = None
        self._running = False
    
    def set_message_handlers(self, handlers: Dict[str, Callable]):
        """Set message handlers for different command types"""
        self.message_handlers = handlers
    
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when the client receives a CONNACK response from the server"""
        if rc == 0:
            self.connected = True
            logger.info("✅ Connected to MQTT broker")
            
            # Subscribe to all command topics
            for topic_type, topic_pattern in self.command_topics.items():
                client.subscribe(topic_pattern)
                logger.info(f"📡 Subscribed to {topic_pattern}")
                
        else:
            self.connected = False
            logger.error(f"❌ Failed to connect to MQTT broker, return code: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for when the client disconnects from the server"""
        self.connected = False
        if rc != 0:
            logger.warning("🔌 Unexpected disconnection from MQTT broker")
        else:
            logger.info("🔌 Disconnected from MQTT broker")
    
    def _on_message(self, client, userdata, msg):
        """Callback for when a PUBLISH message is received from the server"""
        try:
            # Parse topic to determine command type and command name
            topic_parts = msg.topic.split('/')
            if len(topic_parts) < 4:
                logger.warning(f"⚠️ Invalid topic format: {msg.topic}")
                return
            
            device_id = topic_parts[1]
            command_type = topic_parts[2]  # electronic, system, sale, storage
            command_name = topic_parts[3]  # section_open, check_doors, etc.
            
            # Verify device ID matches
            if device_id != self.device_id:
                logger.warning(f"⚠️ Message for different device: {device_id}")
                return
            
            # Parse JSON payload
            try:
                payload = json.loads(msg.payload.decode("utf-8"))
            except json.JSONDecodeError:
                logger.error(f"❌ Failed to decode JSON payload: {msg.payload}")
                return
            
            logger.info(f"📩 Received {command_type}/{command_name}: {payload}")
            
            # Route to appropriate handler
            handler_key = f"{command_type}_{command_name}"
            if handler_key in self.message_handlers:
                try:
                    response = self.message_handlers[handler_key](payload)
                    if response:
                        self.publish_response(response)
                except Exception as e:
                    logger.error(f"❌ Error in handler {handler_key}: {e}")
                    self.publish_response({"error": str(e)})
            else:
                logger.warning(f"⚠️ No handler found for {handler_key}")
                self.publish_response({"error": f"Unknown command: {command_type}/{command_name}"})
                
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
    
    def publish_response(self, response: Dict[str, Any]):
        """Publish response to the response topic"""
        if not self.connected or not self.client:
            logger.error("❌ Cannot publish response: not connected to MQTT broker")
            return False
        
        try:
            response_json = json.dumps(response)
            result = self.client.publish(self.response_topic, response_json)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"📤 Response sent to {self.response_topic}: {response}")
                return True
            else:
                logger.error(f"❌ Failed to publish response, return code: {result.rc}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error publishing response: {e}")
            return False
    
    def start(self):
        """Start the MQTT client in a separate thread"""
        if self._running:
            logger.warning("⚠️ MQTT client is already running")
            return
        
        if not self.broker:
            logger.error("❌ MQTT_HOST not configured")
            return
        
        self._running = True
        self._thread = threading.Thread(target=self._run_client, daemon=True)
        self._thread.start()
        logger.info("🚀 MQTT client started")
    
    def _run_client(self):
        """Run the MQTT client loop"""
        try:
            # Create MQTT client
            self.client = mqtt.Client(client_id=self.client_id)
            
            # Set authentication if provided
            if self.username and self.password:
                self.client.username_pw_set(self.username, self.password)
            
            # Set callbacks
            self.client.on_connect = self._on_connect
            self.client.on_disconnect = self._on_disconnect
            self.client.on_message = self._on_message
            
            # Connect to broker
            logger.info(f"🔗 Connecting to MQTT broker at {self.broker}:{self.port}")
            self.client.connect(self.broker, self.port, 60)
            
            # Start the loop
            while self._running:
                self.client.loop(timeout=1.0)
                
        except Exception as e:
            logger.error(f"❌ MQTT client error: {e}")
        finally:
            if self.client:
                self.client.disconnect()
            self.connected = False
            logger.info("🛑 MQTT client stopped")
    
    def stop(self):
        """Stop the MQTT client"""
        if not self._running:
            return
        
        logger.info("🛑 Stopping MQTT client...")
        self._running = False
        
        if self.client and self.connected:
            self.client.disconnect()
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5.0)
        
        logger.info("✅ MQTT client stopped")
    
    def is_connected(self) -> bool:
        """Check if the client is connected to the MQTT broker"""
        return self.connected
