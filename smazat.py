import json
import os
from dotenv import load_dotenv
import paho.mqtt.client as mqtt

# Načtení proměnných z .env souboru
load_dotenv()

BROKER = os.getenv("MQTT_HOST")
PORT = int(os.getenv("MQTT_PORT", 1883))
USERNAME = os.getenv("MQTT_USERNAME")
PASSWORD = os.getenv("MQTT_PASSWORD")
CLIENT_ID = os.getenv("MQTT_CLIENT_ID", "python-client")

# Topic konstanty
COMMAND_TOPIC = "devices/1234/commands/electronic/unlock"
RESPONSE_TOPIC = "devices/1234/responses"

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("✅ Připojeno k MQTT brokeru")
        client.subscribe(COMMAND_TOPIC)
        print(f"📡 Naslouchám na {COMMAND_TOPIC}")
    else:
        print(f"❌ Nepřipojeno, kód chyby: {rc}")

def on_message(client, userdata, msg):
    try:
        payload = json.loads(msg.payload.decode("utf-8"))
        print(f"📩 Přijatá zpráva: {payload}")

        if payload.get("section_id") == 1:
            print("🔓 Dostali jsme sekci 1! Odemknutí povoleno.")
            response = {"status": "unlocked"}
        else:
            print("⚠ Dostali jsme jinou sekci, ignorujeme.")
            response = {"status": "ignored"}

        # Odeslání odpovědi zpět
        client.publish(RESPONSE_TOPIC, json.dumps(response))
        print(f"📤 Odesláno do {RESPONSE_TOPIC}: {response}")

    except json.JSONDecodeError:
        print("❌ Nepodařilo se dekódovat JSON:", msg.payload)

client = mqtt.Client(client_id=CLIENT_ID)
client.username_pw_set(USERNAME, PASSWORD)
client.on_connect = on_connect
client.on_message = on_message

client.connect(BROKER, PORT, 60)
client.loop_forever()
