"""
MQTT Command Handlers

Handles different types of MQTT commands for electronic, system, sale, and storage operations.
"""

import logging
import mysql.connector
from typing import Dict, Any, Optional
from os import getenv
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


def _get_db_connection():
    """Get database connection"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )


class MQTTHandlers:
    """MQTT command handlers for different device operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    # Electronic Commands
    def electronic_section_open(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle section_open command - open a specific section"""
        try:
            section_id = payload.get("section_id")
            if section_id is None:
                return {"success": False, "error": "section_id is required"}
            
            self.logger.info(f"📩 Electronic: Opening section {section_id}")
            
            # TODO: Implement actual hardware control
            # For now, just simulate success
            # You can integrate with hardware.electronics_api.send_command here
            
            return {"success": True}
            
        except Exception as e:
            self.logger.error(f"Error in electronic_section_open: {e}")
            return {"success": False, "error": str(e)}
    
    def electronic_check_doors(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle check_doors command - check door state of a section"""
        try:
            section_id = payload.get("section_id")
            if section_id is None:
                return {"door_state": "unknown", "error": "section_id is required"}
            
            self.logger.info(f"📩 Electronic: Checking doors for section {section_id}")
            
            # TODO: Implement actual hardware door state check
            # For now, simulate door state
            # You can integrate with hardware.electronics_api.send_command here
            
            return {"door_state": "closed"}  # or "open"
            
        except Exception as e:
            self.logger.error(f"Error in electronic_check_doors: {e}")
            return {"door_state": "unknown", "error": str(e)}
    
    def electronic_unlock_service(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle unlock_service command - unlock service compartment"""
        try:
            self.logger.info("📩 Electronic: Unlocking service compartment")
            
            # TODO: Implement actual service unlock
            # For now, just simulate success
            
            return {"success": True}
            
        except Exception as e:
            self.logger.error(f"Error in electronic_unlock_service: {e}")
            return {"success": False, "error": str(e)}
    
    def electronic_check_service(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle check_service command - check service compartment state"""
        try:
            self.logger.info("📩 Electronic: Checking service compartment state")
            
            # TODO: Implement actual service state check
            # For now, simulate service state
            
            return {"service_state": "locked"}  # or "unlocked"
            
        except Exception as e:
            self.logger.error(f"Error in electronic_check_service: {e}")
            return {"service_state": "unknown", "error": str(e)}
    
    # System Commands
    def system_reboot_device(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle reboot_device command - reboot the device"""
        try:
            self.logger.info("📩 System: Device reboot requested")
            
            # TODO: Implement actual device reboot logic
            # This is an empty function as requested - to be filled later
            
            return {"success": True, "message": "Reboot command received"}
            
        except Exception as e:
            self.logger.error(f"Error in system_reboot_device: {e}")
            return {"success": False, "error": str(e)}
    
    # Sale Commands
    def sale_edit_reservation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle edit_reservation command - edit sale reservation"""
        try:
            uuid = payload.get("uuid")
            status = payload.get("status")
            max_days = payload.get("max_days")
            
            if not uuid:
                return {"success": False, "error": "uuid is required"}
            
            self.logger.info(f"📩 Sale: Editing reservation {uuid}")
            
            db = _get_db_connection()
            cursor = db.cursor()
            
            try:
                # Find reservation by UUID
                cursor.execute("""
                    SELECT id FROM sale_reservations 
                    WHERE uuid = %s
                """, (uuid,))
                
                reservation = cursor.fetchone()
                if not reservation:
                    return {"success": False, "error": "Reservation not found"}
                
                # Update reservation
                update_fields = []
                update_values = []
                
                if status is not None:
                    update_fields.append("status = %s")
                    update_values.append(status)
                
                if max_days is not None:
                    update_fields.append("max_days = %s")
                    update_values.append(max_days)
                
                if update_fields:
                    update_fields.append("last_update = NOW()")
                    update_values.append(uuid)
                    
                    query = f"""
                        UPDATE sale_reservations 
                        SET {', '.join(update_fields)}
                        WHERE uuid = %s
                    """
                    
                    cursor.execute(query, update_values)
                    db.commit()
                
                return {"success": True}
                
            except mysql.connector.Error as err:
                self.logger.error(f"Database error in sale_edit_reservation: {err}")
                return {"success": False, "error": "Database error"}
            finally:
                cursor.close()
                db.close()
                
        except Exception as e:
            self.logger.error(f"Error in sale_edit_reservation: {e}")
            return {"success": False, "error": str(e)}
    
    # Storage Commands
    def storage_storage_edit_reservation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle storage_edit_reservation command - edit storage reservation"""
        try:
            uuid = payload.get("uuid")
            status = payload.get("status")
            max_days = payload.get("max_days")
            
            if not uuid:
                return {"success": False, "error": "uuid is required"}
            
            self.logger.info(f"📩 Storage: Editing reservation {uuid}")
            
            db = _get_db_connection()
            cursor = db.cursor()
            
            try:
                # Find reservation by UUID
                cursor.execute("""
                    SELECT id FROM storage_reservations 
                    WHERE uuid = %s
                """, (uuid,))
                
                reservation = cursor.fetchone()
                if not reservation:
                    return {"success": False, "error": "Reservation not found"}
                
                # Update reservation
                update_fields = []
                update_values = []
                
                if status is not None:
                    update_fields.append("status = %s")
                    update_values.append(status)
                
                if max_days is not None:
                    update_fields.append("max_days = %s")
                    update_values.append(max_days)
                
                if update_fields:
                    update_fields.append("last_update = NOW()")
                    update_values.append(uuid)
                    
                    query = f"""
                        UPDATE storage_reservations 
                        SET {', '.join(update_fields)}
                        WHERE uuid = %s
                    """
                    
                    cursor.execute(query, update_values)
                    db.commit()
                
                return {"success": True}
                
            except mysql.connector.Error as err:
                self.logger.error(f"Database error in storage_storage_edit_reservation: {err}")
                return {"success": False, "error": "Database error"}
            finally:
                cursor.close()
                db.close()
                
        except Exception as e:
            self.logger.error(f"Error in storage_storage_edit_reservation: {e}")
            return {"success": False, "error": str(e)}


# Create handlers instance and get handler functions
handlers = MQTTHandlers()

# Handler mapping for MQTT client
MQTT_HANDLERS = {
    # Electronic commands
    "electronic_section_open": handlers.electronic_section_open,
    "electronic_check_doors": handlers.electronic_check_doors,
    "electronic_unlock_service": handlers.electronic_unlock_service,
    "electronic_check_service": handlers.electronic_check_service,
    
    # System commands
    "system_reboot_device": handlers.system_reboot_device,
    
    # Sale commands
    "sale_edit_reservation": handlers.sale_edit_reservation,
    
    # Storage commands
    "storage_storage_edit_reservation": handlers.storage_storage_edit_reservation,
}
